---
import Layout from '../layouts/Layout.astro';
import { createPolarClient, transformPolarProduct } from '../lib/polar';
import type { LocalProduct } from '../types/product';

// Get search query from URL params
const url = Astro.url;
const searchQuery = url.searchParams.get('q') || '';

let products: LocalProduct[] = [];
let error: string | null = null;

// Fetch products if there's a search query
if (searchQuery.trim()) {
  try {
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

    if (organizationId) {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });

      const productList = response.result?.items || [];
      const allProducts = productList
        .map(transformPolarProduct)
        .filter((product): product is LocalProduct => product !== null);

      // Filter products by search query
      const searchLower = searchQuery.toLowerCase();
      products = allProducts.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower) ||
        (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchLower)))
      );
    } else {
      error = 'Organization ID not configured';
    }
  } catch (err) {
    console.error('Search error:', err);
    error = 'Failed to search products';
  }
}

// Get popular tags for suggestions
let popularTags: string[] = [];
try {
  const polar = createPolarClient();
  const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
  
  if (organizationId) {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const tagCounts: { [key: string]: number } = {};
    
    productList.forEach(product => {
      if (product.metadata) {
        Object.entries(product.metadata).forEach(([key, value]) => {
          if (key.startsWith('tag:') && typeof value === 'string') {
            const tag = value;
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          }
        });
      }
    });

    popularTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([tag]) => tag);
  }
} catch (err) {
  console.error('Failed to fetch popular tags:', err);
}
---

<Layout
  title={searchQuery ? `Search: ${searchQuery} - Polar Image Store` : 'Search - Polar Image Store'}
  description="Search our collection of premium digital images and artwork"
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/search`}
>
  <div class="min-h-screen bg-white">
    <!-- Header with back button -->
    <div class="sticky top-0 z-50 bg-white border-b border-gray-200">
      <div class="flex items-center px-4 py-3">
        <button 
          id="backButton"
          class="mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Go back"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <!-- Search Input -->
        <div class="flex-1 relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            id="searchInput"
            value={searchQuery}
            placeholder="Search for images..."
            class="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-full bg-gray-50 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 focus:bg-white transition-all text-lg"
            autocomplete="off"
            autofocus
          />
          
          <!-- Clear button -->
          <button
            id="clearButton"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
            style="display: none;"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="px-4 py-6">
      {searchQuery ? (
        <!-- Search Results -->
        <div>
          <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Search Results</h1>
            <p class="text-gray-600">
              {products.length > 0
                ? `Found ${products.length} result${products.length === 1 ? '' : 's'} for "${searchQuery}"`
                : `No results found for "${searchQuery}"`
              }
            </p>
          </div>

          {error ? (
            <div class="text-center py-12">
              <div class="text-red-600 mb-4">
                <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="text-lg font-medium">{error}</p>
              </div>
            </div>
          ) : products.length > 0 ? (
            <!-- Products Grid -->
            <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
              {products.map((product) => (
                <a
                  href={`/products/${product.slug}`}
                  class="group block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div class="aspect-square bg-gray-100">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        loading="lazy"
                      />
                    ) : (
                      <div class="w-full h-full flex items-center justify-center text-gray-400">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div class="p-3">
                    <h3 class="font-medium text-gray-900 text-sm line-clamp-2 mb-1">{product.name}</h3>
                    <p class="text-accent-600 font-semibold text-sm">
                      ${(product.price / 100).toFixed(2)}
                    </p>
                  </div>
                </a>
              ))}
            </div>
          ) : (
            <!-- No Results -->
            <div class="text-center py-12">
              <div class="text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <p class="text-lg font-medium text-gray-900 mb-2">No results found</p>
                <p class="text-gray-600 mb-6">Try searching with different keywords</p>
              </div>
              
              <!-- Suggestions -->
              {popularTags.length > 0 && (
                <div class="max-w-md mx-auto">
                  <p class="text-sm font-medium text-gray-900 mb-3">Popular tags:</p>
                  <div class="flex flex-wrap gap-2 justify-center">
                    {popularTags.slice(0, 6).map((tag) => (
                      <button
                        class="tag-suggestion px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        data-tag={tag}
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      ) : (
        <!-- Initial Search State -->
        <div>
          <!-- Recent Searches -->
          <div id="recentSearches" class="mb-8" style="display: none;">
            <h2 class="text-lg font-semibold text-gray-900 mb-3">Recent Searches</h2>
            <div id="recentSearchesList" class="space-y-2">
              <!-- Recent searches will be populated here -->
            </div>
          </div>

          <!-- Popular Tags -->
          {popularTags.length > 0 && (
            <div class="mb-8">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h2>
              <div class="grid grid-cols-2 gap-3">
                {popularTags.map((tag) => (
                  <button
                    class="tag-suggestion flex items-center justify-between p-3 bg-gray-50 rounded-lg text-left hover:bg-gray-100 transition-colors"
                    data-tag={tag}
                  >
                    <span class="font-medium text-gray-900">{tag}</span>
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                ))}
              </div>
            </div>
          )}

          <!-- Quick Actions -->
          <div>
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Browse</h2>
            <div class="space-y-3">
              <a
                href="/products"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span class="font-medium text-gray-900">All Products</span>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  </div>
</Layout>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchInput') as HTMLInputElement;
    const clearButton = document.getElementById('clearButton') as HTMLButtonElement;
    const backButton = document.getElementById('backButton') as HTMLButtonElement;
    const tagSuggestions = document.querySelectorAll('.tag-suggestion');
    const recentSearchesContainer = document.getElementById('recentSearches');
    const recentSearchesList = document.getElementById('recentSearchesList');

    let searchTimeout: any;

    // Back button functionality
    if (backButton) {
      backButton.addEventListener('click', () => {
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = '/';
        }
      });
    }

    // Search input functionality
    if (searchInput) {
      // Auto-focus on mobile
      if (window.innerWidth < 768) {
        setTimeout(() => {
          searchInput.focus();
        }, 100);
      }

      // Show/hide clear button
      const toggleClearButton = () => {
        if (clearButton) {
          clearButton.style.display = searchInput.value.trim() ? 'flex' : 'none';
        }
      };

      searchInput.addEventListener('input', () => {
        toggleClearButton();
        
        // Clear previous timeout
        if (searchTimeout) {
          clearTimeout(searchTimeout);
        }

        // Debounced search
        const query = searchInput.value.trim();
        if (query.length >= 2) {
          searchTimeout = setTimeout(() => {
            performSearch(query);
          }, 500);
        }
      });

      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = searchInput.value.trim();
          if (query) {
            performSearch(query);
          }
        }
      });

      // Initial toggle
      toggleClearButton();
    }

    // Clear button functionality
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        searchInput.value = '';
        clearButton.style.display = 'none';
        searchInput.focus();
        
        // Clear URL and reload to show initial state
        window.history.replaceState({}, '', '/search');
        window.location.reload();
      });
    }

    // Tag suggestion functionality
    tagSuggestions.forEach(button => {
      button.addEventListener('click', () => {
        const tag = button.getAttribute('data-tag');
        if (tag) {
          searchInput.value = tag;
          performSearch(tag);
        }
      });
    });

    // Search functionality
    function performSearch(query: string) {
      // Save to recent searches
      saveRecentSearch(query);
      
      // Navigate to search results
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }

    // Recent searches functionality
    function saveRecentSearch(query: string) {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        const filtered = recent.filter((item: string) => item !== query);
        filtered.unshift(query);
        const limited = filtered.slice(0, 5);
        localStorage.setItem('recentSearches', JSON.stringify(limited));
      } catch (error) {
        console.error('Failed to save recent search:', error);
      }
    }

    function loadRecentSearches() {
      try {
        const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
        if (recent.length > 0 && recentSearchesContainer && recentSearchesList) {
          recentSearchesContainer.style.display = 'block';
          recentSearchesList.innerHTML = recent.map((query: string) => `
            <button class="recent-search-item flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg text-left hover:bg-gray-100 transition-colors" data-query="${query}">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-gray-900">${query}</span>
              </div>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          `).join('');

          // Add click listeners to recent search items
          document.querySelectorAll('.recent-search-item').forEach(item => {
            item.addEventListener('click', () => {
              const query = item.getAttribute('data-query');
              if (query) {
                searchInput.value = query;
                performSearch(query);
              }
            });
          });
        }
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }

    // Load recent searches on initial load (only if no search query)
    if (!searchInput.value.trim()) {
      loadRecentSearches();
    }
  });
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
